# VIClass Awareness System

The VIClass Awareness System is a real-time collaborative feature that enables users to share their current state and actions with other users in a collaborative environment. It provides visibility into what other users are doing, including document editing, loading operations, notifications, and transient events like mouse movements.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Core Components](#core-components)
- [Awareness Types](#awareness-types)
- [Flow Diagrams](#flow-diagrams)
- [Usage Examples](#usage-examples)
- [Best Practices](#best-practices)
- [API Reference](#api-reference)

## Overview

The Awareness System consists of three main layers:

1. **Feature Layer** (`AwarenessFeature`) - Manages awareness across multiple viewports
2. **Tool Layer** (`AwarenessTool`) - Handles awareness states within individual viewports
3. **Command Layer** (`SendAwarenessStateCmd`, `ClearAwarenessStateCmd`) - Network communication

### Key Features

- **Real-time synchronization** of user activities across multiple clients
- **Automatic expiration** of awareness states to prevent stale information
- **Multiple awareness types** for different use cases
- **Scheduled updates** for persistent awareness states
- **Garbage collection** to manage memory usage
- **Flexible configuration** for different awareness behaviors

## Architecture

```mermaid
graph TB
    subgraph "Client A"
        AF1[AwarenessFeature]
        AT1[AwarenessTool]
        UI1[UI Component]
    end

    subgraph "Client B"
        AF2[AwarenessFeature]
        AT2[AwarenessTool]
        UI2[UI Component]
    end

    subgraph "Network Layer"
        CC[Command Channel]
        SAC[SendAwarenessStateCmd]
        CAC[ClearAwarenessStateCmd]
    end

    UI1 --> AT1
    AT1 --> AF1
    AF1 --> SAC
    SAC --> CC
    CC --> SAC
    SAC --> AF2
    AF2 --> AT2
    AT2 --> UI2

    UI2 --> AT2
    AT2 --> AF2
    AF2 --> CAC
    CAC --> CC
    CC --> CAC
    CAC --> AF1
```

## Core Components

### AwarenessFeature

The central coordinator that manages awareness across multiple viewports.

**Key Responsibilities:**

- Register and manage `AwarenessTool` instances for each viewport
- Coordinate awareness state distribution between viewports
- Run garbage collection to remove expired awareness states
- Provide utility methods for sending different types of awareness

**Key Methods:**

- `addAwarenessTool(vmId, tool)` - Register an awareness tool
- `sendAwarenessCommand(viewportId, message, options)` - Send awareness state
- `getReceivedAwareness(vpId)` - Get awareness states for a viewport
- `useAwareness(viewportId, message, options, callback)` - Execute callback with awareness

### AwarenessTool

Manages awareness states within a single viewport.

**Key Responsibilities:**

- Send awareness states to other users
- Receive and process awareness states from other users
- Schedule periodic updates for persistent awareness
- Handle awareness state lifecycle (creation, updates, expiration)

**Key Methods:**

- `sendAwarenessState(message, options)` - Create and send awareness
- `clearAwarenessState(awarenessId)` - Remove awareness state
- `applySendAwarenessStateAsReceiver(awareness, isLocal)` - Process received awareness

### AwarenessToolState

Maintains the state data for an `AwarenessTool` instance.

**Properties:**

- `receivedAwareness: BehaviorSubject<Awareness[]>` - Observable of active awareness states
- `markDeletedAwareness: { [awarenessId: string]: number }` - Tracks deleted awareness with timestamps

## Awareness Types

### 1. Document Awareness (`aw-document`)

Used to show what a user is doing with a specific document element.

**Configuration:**

- `useScheduler: true` - Periodically resends to maintain visibility
- `startAfterSeconds: 1` - Delay before showing to avoid flashing
- `expireAfterSeconds: 5` - Expires after 5 seconds of inactivity

**Use Cases:**

- User editing a specific document
- User viewing a particular section
- User performing document operations

### 2. Loading Awareness (`aw-loading`)

Used for long-running operations that need progress indication.

**Configuration:**

- `useScheduler: true` - Maintains visibility during operation
- `startAfterSeconds: 5` - Only shows for operations longer than 5 seconds
- `expireAfterSeconds: 1` - Expires quickly after operation completes

**Use Cases:**

- Document paste operations
- File uploads
- Data processing tasks

### 3. One-shot Awareness (`aw-oneshot`)

Used for transient events that don't need persistence.

**Configuration:**

- `useScheduler: false` - No periodic updates needed
- `startAfterSeconds: 0` - Send immediately
- `expireAfterSeconds: 0` - Expire immediately after processing

**Use Cases:**

- Mouse movements
- Cursor positions
- Temporary interactions

### 4. Notification Awareness (`aw-notification`)

Used to display messages to users.

**Configuration:**

- `useScheduler: false` - No periodic updates needed
- `isNotSendToLocalReceiver: false` - Local user receives by default
- `isNotSendToRemoteReceiver: true` - Remote users don't receive by default

**Use Cases:**

- Success messages
- Error notifications
- Information alerts

## Flow Diagrams

### Awareness State Creation and Propagation

```mermaid
sequenceDiagram
    participant UI as UI Component
    participant AT as AwarenessTool
    participant AF as AwarenessFeature
    participant CC as Command Channel
    participant RT as Remote Tool

    UI->>AT: sendAwarenessState(message, options)
    AT->>AT: Create Awareness object
    AT->>AT: Add to scheduler (if useScheduler=true)
    AT->>CC: Send SendAwarenessStateCmd
    CC->>RT: Forward command
    RT->>RT: applySendAwarenessStateAsReceiver()
    RT->>RT: Update receivedAwareness observable
    RT->>UI: UI updates via observable subscription
```

### Awareness State Expiration

```mermaid
sequenceDiagram
    participant GC as Garbage Collector
    participant AT as AwarenessTool
    participant AF as AwarenessFeature

    loop Every 1 second
        GC->>AF: Check all tools
        AF->>AT: Check receivedAwareness
        AT->>AT: Filter expired awareness
        alt Has expired awareness
            AT->>AT: Update receivedAwareness
            AT->>UI: Notify subscribers
        end
    end
```

## Usage Examples

### Basic Awareness Setup

```typescript
import { AwarenessFeature, AwarenessTool } from '@viclass/editor.core';

// 1. Create awareness feature (typically done in coordinator)
const awarenessFeature = new AwarenessFeature(coordinator);

// 2. Create and register awareness tool for a viewport
const awarenessTool = new AwarenessTool(awarenessFeature, cmdChannel, userId);
awarenessFeature.addAwarenessTool(viewportId, awarenessTool);
```

### Sending Different Types of Awareness

#### Document Awareness

```typescript
import { buildDocumentAwarenessCmdOption, initRandomAwarenessId } from '@viclass/editor.core';

// Show user editing a document
const awarenessId = awarenessFeature.sendAwarenessCommand(
    viewportId,
    'User is editing document',
    buildDocumentAwarenessCmdOption(initRandomAwarenessId(), docCtrl)
);
```

#### Loading Awareness

```typescript
import { buildLoadingAwarenessCmdOption } from '@viclass/editor.core';

// Show loading operation
const awarenessId = awarenessFeature.sendAwarenessCommand(
    viewportId,
    'Pasting document...',
    buildLoadingAwarenessCmdOption('paste-operation', { operation: 'paste' })
);

// Clear when operation completes
await awarenessFeature.clearAwarenessCommand(viewportId, awarenessId);
```

#### One-shot Awareness (Mouse Movement)

```typescript
import { buildOneshotAwarenessCmdOption } from '@viclass/editor.core';

// Send mouse position
awarenessFeature.sendAwarenessCommand(
    viewportId,
    '',
    buildOneshotAwarenessCmdOption('mouse-position', {
        x: mouseX,
        y: mouseY,
        cursors: currentCursors,
    })
);
```

#### Notification Awareness

```typescript
import { sendNotiMessage } from '@viclass/editor.core';

// Send success notification
await sendNotiMessage(awarenessFeature, viewportId, 'Document saved successfully!', 'success');

// Send error notification
await sendNotiMessage(awarenessFeature, viewportId, 'Failed to save document', 'error');
```

### Using Awareness with Callbacks

```typescript
// Execute operation with loading awareness
const result = await awarenessFeature.useAwareness(
    viewportId,
    'Processing data...',
    buildLoadingAwarenessCmdOption('data-processing'),
    async () => {
        // Your long-running operation here
        return await processLargeDataset();
    },
    true // Auto-clear awareness when done
);
```

### Receiving and Displaying Awareness

#### Angular Component Example

```typescript
import { Component, Input, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';

@Component({
    selector: 'awareness-display',
    template: `
        <div *ngIf="(awareness$ | async)?.length" class="awareness-container">
            <img src="assets/img/mini-spinner.svg" />
            <p *ngFor="let item of awareness$ | async">{{ item.message }}</p>
        </div>
    `,
})
export class AwarenessComponent implements OnInit {
    @Input() coord: EditorCoordinator;
    @Input() vpId: string;

    awareness$: Observable<Awareness[]>;

    ngOnInit() {
        this.awareness$ =
            this.coord.awarenessFeature.getReceivedAwareness(this.vpId)?.pipe(
                tap(this.handleNotifications),
                map(awareness => awareness.filter(a => ['aw-document', 'aw-loading'].includes(a.options.type)))
            ) ?? of([]);
    }

    private handleNotifications = (awareness: Awareness[]) => {
        awareness
            .filter(a => a.options.type === 'aw-notification')
            .forEach(a => {
                this.notificationService.showNotification({
                    message: a.message,
                    status: a.options.payload?.msgType ?? 'info',
                    duration: 2000,
                });
            });
    };
}
```

### Custom Awareness Type Implementation

```typescript
// Define custom awareness type
export const buildCustomAwarenessCmdOption = (id: string, customData: any): AwarenessCmdOption => ({
    type: 'aw-loading', // Use existing type as base
    id,
    useScheduler: true,
    startAfterSeconds: 0,
    expireAfterSeconds: 10,
    payload: {
        customType: 'my-custom-awareness',
        data: customData,
    },
});

// Usage
const customAwarenessId = awarenessFeature.sendAwarenessCommand(
    viewportId,
    'Custom operation in progress',
    buildCustomAwarenessCmdOption('custom-op', { step: 1, total: 5 })
);
```

## Best Practices

### 1. Awareness Lifecycle Management

**DO:**

- Always clear awareness states when operations complete
- Use appropriate expiration times for different awareness types
- Use `useAwareness()` for operations with clear start/end points

**DON'T:**

- Leave awareness states running indefinitely
- Use very short expiration times for loading operations
- Send awareness for every minor UI interaction

### 2. Performance Considerations

**DO:**

- Use one-shot awareness for high-frequency events (mouse movements)
- Batch similar awareness updates when possible
- Set reasonable scheduler intervals (default: 3 seconds)

**DON'T:**

- Send awareness updates more frequently than necessary
- Use scheduled awareness for transient events
- Create awareness for every user action

### 3. User Experience

**DO:**

- Use meaningful, user-friendly messages
- Show loading awareness only for operations > 5 seconds
- Provide context about what the user is doing

**DON'T:**

- Show technical error messages to end users
- Display awareness for instantaneous operations
- Overwhelm users with too many awareness indicators

### 4. Error Handling

```typescript
try {
    const awarenessId = awarenessFeature.sendAwarenessCommand(
        viewportId,
        'Processing...',
        buildLoadingAwarenessCmdOption('operation')
    );

    await performOperation();

    // Always clear in finally block
    await awarenessFeature.clearAwarenessCommand(viewportId, awarenessId);
} catch (error) {
    // Send error notification
    await sendNotiMessage(awarenessFeature, viewportId, 'Operation failed', 'error');
}
```

## API Reference

### AwarenessFeature

#### Methods

##### `addAwarenessTool(vmId: ViewportId, tool: AwarenessTool): void`

Registers an awareness tool for a specific viewport.

##### `sendAwarenessCommand(viewportId: string, message: string, options?: AwarenessCmdOption): string | null`

Sends an awareness command to a specific viewport.

**Returns:** The ID of the created awareness, or null if the tool wasn't found.

##### `clearAwarenessCommand(viewportId: string, awarenessId: string): Promise<boolean>`

Clears an awareness command from a specific viewport.

**Returns:** True if the awareness was cleared, false otherwise.

##### `useAwareness(viewportId: string, message: string, options?: AwarenessCmdOption, cb?: () => any, isSendClearCmd = true): Promise<any>`

Sends awareness, executes callback, then optionally clears awareness.

##### `getReceivedAwareness(vpId: ViewportId): BehaviorSubject<Awareness[]> | undefined`

Retrieves the received awareness states for a specific viewport.

##### `getReceivedAwarenessOfAllViewport(): Observable<Awareness[]>`

Retrieves all awareness states from all registered viewports.

### AwarenessTool

#### Methods

##### `sendAwarenessState(message: string, options?: AwarenessCmdOption): string`

Creates and sends an awareness state.

**Returns:** The ID of the created awareness.

##### `clearAwarenessState(awarenessId: string): Promise<boolean | null>`

Clears a specific awareness state.

### Utility Functions

##### `initRandomAwarenessId(): string`

Generates a random unique ID for awareness instances.

##### `buildDocumentAwarenessCmdOption(id: string, docCtrl: VDocCtrl): AwarenessCmdOption`

Creates awareness options for document-related events.

##### `buildOneshotAwarenessCmdOption(id: string, payload?: any): AwarenessCmdOption`

Creates awareness options for one-shot events.

##### `buildLoadingAwarenessCmdOption(id: string, payload?: any): AwarenessCmdOption`

Creates awareness options for loading events.

##### `buildNotificationAwarenessCmdOption(id: string, option?: Partial<AwarenessCmdOption>): AwarenessCmdOption`

Creates awareness options for notification messages.

##### `sendNotiMessage(awFeature: AwarenessFeature, viewportId: string, message: string, msgType: 'success' | 'error' | 'info', options?: Partial<AwarenessCmdOption>): Promise<string>`

Convenience function for sending notification messages.

### Types

#### `Awareness`

```typescript
type Awareness = {
    viewportId: string;
    userId?: string;
    message: string;
    updatedAt: number;
    options: AwarenessCmdOption;
};
```

#### `AwarenessCmdOption`

```typescript
type AwarenessCmdOption = (
    | { type: 'aw-document'; docInfo: { localId: number; globalId?: string } }
    | { type: 'aw-oneshot' | 'aw-loading' | 'aw-notification' }
) & {
    id?: string;
    useScheduler?: boolean;
    startAfterSeconds?: number;
    expireAfterSeconds?: number;
    schedulerInterval?: number;
    isNotSendToLocalReceiver?: boolean;
    isNotSendToRemoteReceiver?: boolean;
    payload?: any;
};
```

---

For more detailed implementation examples, see the source files:

- `awareness.feature.ts` - Main feature implementation
- `awareness.tool.ts` - Tool implementation
- `awareness.state.cmd.ts` - Command definitions

Creates awareness options for notification messages.

##### `sendNotiMessage(awFeature: AwarenessFeature, viewportId: string, message: string, msgType: 'success' | 'error' | 'info', options?: Partial<AwarenessCmdOption>): Promise<string>`

Convenience function for sending notification messages.
